
import React, { useState, useEffect } from 'react';
import { FileText, Plus, Edit, Trash2, Eye, RefreshCw, AlertTriangle, Bug, Clock, Database, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import AdminLayout from '@/components/layout/AdminLayout';
import BlogEditor from '@/components/BlogEditor';
import BlogWorkflowTester from '@/components/BlogWorkflowTester';
import AutosaveTester from '@/components/AutosaveTester';
import BlogCollectionInitializer from '@/components/BlogCollectionInitializer';
import AuthDebugger from '@/components/AuthDebugger';
import { Blog, BlogFormData, CreateBlogData, CreatePublishedBlogData } from '@/types';
import { getAllBlogs, createBlog, updateBlog, deleteBlog, togglePublishStatus, publishBlogPost, testFirebaseConnection } from '@/lib/blogService';
import { useAuth } from '@/lib/AuthContext';
import { toast } from 'sonner';
import { useBlogDebugger, logAuthState } from '@/utils/debugBlogService';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

const AdminBlogManager: React.FC = () => {
  const { currentUser, isAdmin } = useAuth();
  const { runAllTests } = useBlogDebugger();
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingBlog, setEditingBlog] = useState<Blog | null>(null);
  const [editorMode, setEditorMode] = useState<'create' | 'edit'>('create');
  const [isProcessing, setIsProcessing] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [blogToDelete, setBlogToDelete] = useState<Blog | null>(null);
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const [showWorkflowTester, setShowWorkflowTester] = useState(false);
  const [showAutosaveTester, setShowAutosaveTester] = useState(false);
  const [showCollectionInitializer, setShowCollectionInitializer] = useState(false);
  const [showAuthDebugger, setShowAuthDebugger] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    fetchBlogs();
  }, []);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      setError(null);
      const allBlogs = await getAllBlogs();
      setBlogs(allBlogs);
    } catch (error) {
      console.error('Error fetching blogs:', error);
      if (error instanceof Error) {
        setError(`Failed to load blogs: ${error.message}`);
      } else {
        setError('Failed to load blogs. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await testFirebaseConnection();

      if (result.success) {
        toast.success('Firebase connection test passed successfully!');
        console.log('Connection test details:', result.details);
      } else {
        toast.error(`Connection test failed: ${result.message}`);
        console.error('Connection test details:', result.details);
        setError(`Connection test failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Connection test error: ${errorMessage}`);
      setError(`Connection test error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const runDebugTests = async () => {
    try {
      setLoading(true);
      setError(null);

      // Log current auth state
      logAuthState(currentUser, isAdmin);

      // Run comprehensive debug tests
      const results = await runAllTests();

      if (results.errors.length === 0) {
        toast.success('All debug tests passed successfully!');
      } else {
        toast.error(`Debug tests completed with ${results.errors.length} errors`);
        setError(`Debug errors: ${results.errors.join(', ')}`);
      }

      console.log('Debug test results:', results);
    } catch (error) {
      console.error('Error running debug tests:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Debug test error: ${errorMessage}`);
      setError(`Debug test error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBlog = () => {
    setEditingBlog(null);
    setEditorMode('create');
    setIsEditorOpen(true);
  };

  const handleEditBlog = (blog: Blog) => {
    setEditingBlog(blog);
    setEditorMode('edit');
    setIsEditorOpen(true);
  };

  const handleSaveBlog = async (blogData: BlogFormData) => {
    if (!currentUser) {
      toast.error('You must be logged in to save blogs');
      return;
    }

    try {
      setIsProcessing(true);

      if (editorMode === 'create') {
        if (blogData.published) {
          // Publish to blogPosts collection
          await publishBlogPost({
            title: blogData.title,
            content: blogData.content,
            excerpt: blogData.excerpt,
            author: currentUser.displayName || currentUser.email?.split('@')[0] || 'Admin',
            tags: blogData.tags,
            coverImageUrl: blogData.coverImageUrl,
          });
          toast.success('Blog post published successfully!');
          setIsEditorOpen(false); // Close dialog after publishing
        } else {
          // Save as draft to blogs collection
          const createData: CreateBlogData = {
            ...blogData,
            authorId: currentUser.uid,
            authorName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Admin',
            authorEmail: currentUser.email || '',
          };
          await createBlog(createData);
          toast.success('Draft saved successfully!');
          // Keep dialog open for continued editing
        }
      } else if (editingBlog) {
        if (blogData.published && !editingBlog.published) {
          // Publishing a draft - move to blogPosts collection
          await publishBlogPost({
            title: blogData.title,
            content: blogData.content,
            excerpt: blogData.excerpt,
            author: editingBlog.authorName || 'Admin',
            tags: blogData.tags,
            coverImageUrl: blogData.coverImageUrl,
          });

          // Optionally delete from drafts collection
          // await deleteBlog(editingBlog.id);

          toast.success('Blog post published successfully!');
          setIsEditorOpen(false); // Close dialog after publishing
        } else {
          // Update existing draft or published post
          await updateBlog(editingBlog.id, blogData);
          if (blogData.published) {
            toast.success('Published blog post updated successfully!');
            setIsEditorOpen(false); // Close dialog after updating published post
          } else {
            toast.success('Draft saved successfully!');
            // Keep dialog open for continued editing of drafts
          }
        }
      }

      await fetchBlogs();
    } catch (error) {
      console.error('Error saving blog:', error);

      // Enhanced error handling with specific error messages
      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      if (blogData.published) {
        toast.error(`Failed to publish blog post: ${errorMessage}`);
      } else {
        toast.error(`Failed to save draft: ${errorMessage}`);
      }

      // Log additional context for debugging
      console.error('Blog save error context:', {
        mode: editorMode,
        published: blogData.published,
        hasTitle: !!blogData.title,
        hasContent: !!blogData.content,
        hasAuthor: !!currentUser,
        userEmail: currentUser?.email
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTogglePublish = async (blog: Blog) => {
    try {
      setIsProcessing(true);
      console.log(`Toggling publish status for blog: ${blog.title} (ID: ${blog.id})`);

      const newStatus = await togglePublishStatus(blog.id);

      if (newStatus) {
        toast.success(`"${blog.title}" published successfully! It's now visible on the public blog page.`);
      } else {
        toast.success(`"${blog.title}" unpublished successfully. It's now only visible in the admin dashboard.`);
      }

      // Refresh the blog list to show updated status
      await fetchBlogs();
    } catch (error) {
      console.error('Error toggling publish status:', error);

      let errorMessage = 'Failed to update publish status. Please try again.';
      if (error instanceof Error) {
        if (error.message.includes('permission')) {
          errorMessage = 'Permission denied. Please check your admin access.';
        } else if (error.message.includes('not found')) {
          errorMessage = 'Blog post not found. Please refresh the page and try again.';
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = `Failed to update publish status: ${error.message}`;
        }
      }

      toast.error(errorMessage);
      setError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const openDeleteDialog = (blog: Blog) => {
    setBlogToDelete(blog);
    setDeleteDialogOpen(true);
  };

  const handleDeleteBlog = async () => {
    if (!blogToDelete) return;

    try {
      setIsDeleting(true);
      console.log(`Deleting blog: ${blogToDelete.title} (ID: ${blogToDelete.id})`);

      await deleteBlog(blogToDelete.id);

      toast.success(`"${blogToDelete.title}" deleted successfully from all collections.`);

      // Refresh the blog list
      await fetchBlogs();
      setDeleteDialogOpen(false);
      setBlogToDelete(null);
    } catch (error) {
      console.error('Error deleting blog:', error);

      let errorMessage = 'Failed to delete blog post. Please try again.';
      if (error instanceof Error) {
        if (error.message.includes('permission')) {
          errorMessage = 'Permission denied. Please check your admin access.';
        } else if (error.message.includes('not found')) {
          errorMessage = 'Blog post not found. It may have already been deleted.';
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = `Failed to delete blog post: ${error.message}`;
        }
      }

      toast.error(errorMessage);
      setError(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <AdminLayout title="Blog Manager" description="Create and manage blog posts and content">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Blog Manager</h1>
          <p className="text-gray-600">Create and manage blog posts and content</p>
        </div>

        <div className="flex gap-2 mt-4 md:mt-0">
          <Button
            variant="default"
            onClick={handleCreateBlog}
            className="flex items-center bg-burgundy-600 hover:bg-burgundy-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create New Post
          </Button>

          <Button
            variant="outline"
            onClick={fetchBlogs}
            disabled={loading}
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </>
            )}
          </Button>

          <Button
            variant="outline"
            onClick={testConnection}
            disabled={loading}
            className="border-blue-300 text-blue-600 hover:bg-blue-50"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Test Connection
          </Button>

          <Button
            variant="outline"
            onClick={runDebugTests}
            disabled={loading}
            className="border-purple-300 text-purple-600 hover:bg-purple-50"
          >
            <Bug className="h-4 w-4 mr-2" />
            Run Debug Tests
          </Button>

          <Button
            variant="outline"
            onClick={() => setShowWorkflowTester(!showWorkflowTester)}
            className="border-green-300 text-green-600 hover:bg-green-50"
          >
            <FileText className="h-4 w-4 mr-2" />
            {showWorkflowTester ? 'Hide' : 'Show'} Workflow Tester
          </Button>

          <Button
            variant="outline"
            onClick={() => setShowAutosaveTester(!showAutosaveTester)}
            className="border-orange-300 text-orange-600 hover:bg-orange-50"
          >
            <Clock className="h-4 w-4 mr-2" />
            {showAutosaveTester ? 'Hide' : 'Show'} Autosave Tester
          </Button>

          <Button
            variant="outline"
            onClick={() => setShowCollectionInitializer(!showCollectionInitializer)}
            className="border-indigo-300 text-indigo-600 hover:bg-indigo-50"
          >
            <Database className="h-4 w-4 mr-2" />
            {showCollectionInitializer ? 'Hide' : 'Show'} Collection Setup
          </Button>

          <Button
            variant="outline"
            onClick={() => setShowAuthDebugger(!showAuthDebugger)}
            className="border-red-300 text-red-600 hover:bg-red-50"
          >
            <Shield className="h-4 w-4 mr-2" />
            {showAuthDebugger ? 'Hide' : 'Show'} Auth Debug
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Debug Info Panel */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">Debug Information</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDebugInfo(!showDebugInfo)}
            className="text-xs"
          >
            {showDebugInfo ? 'Hide' : 'Show'} Details
          </Button>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
          <div>
            <span className="font-medium text-gray-600">User:</span>
            <p className="text-gray-800">{currentUser?.email || 'Not logged in'}</p>
          </div>
          <div>
            <span className="font-medium text-gray-600">Admin:</span>
            <p className={`${isAdmin ? 'text-green-600' : 'text-red-600'}`}>
              {isAdmin ? 'Yes' : 'No'}
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-600">Email Verified:</span>
            <p className={`${currentUser?.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
              {currentUser?.emailVerified ? 'Yes' : 'No'}
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-600">Blogs Count:</span>
            <p className="text-gray-800">{blogs.length}</p>
          </div>
        </div>

        {showDebugInfo && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
              <div>
                <span className="font-medium text-gray-600">User ID:</span>
                <p className="text-gray-800 font-mono break-all">{currentUser?.uid || 'N/A'}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Display Name:</span>
                <p className="text-gray-800">{currentUser?.displayName || 'N/A'}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Workflow Tester */}
      {showWorkflowTester && (
        <div className="mb-6">
          <BlogWorkflowTester />
        </div>
      )}

      {/* Autosave Tester */}
      {showAutosaveTester && (
        <div className="mb-6">
          <AutosaveTester />
        </div>
      )}

      {/* Collection Initializer */}
      {showCollectionInitializer && (
        <div className="mb-6">
          <BlogCollectionInitializer />
        </div>
      )}

      {/* Auth Debugger */}
      {showAuthDebugger && (
        <div className="mb-6">
          <AuthDebugger />
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
          <span className="ml-2 text-gray-600">Loading blogs...</span>
        </div>
      ) : blogs.length > 0 ? (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tags
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {blogs.map((blog) => (
                  <tr key={blog.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="max-w-xs">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {blog.title}
                        </div>
                        {blog.excerpt && (
                          <div className="text-sm text-gray-500 truncate">
                            {blog.excerpt}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-1">
                        {blog.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {blog.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{blog.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Badge
                        variant={blog.published ? "default" : "secondary"}
                        className={blog.published ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                      >
                        {blog.published ? 'Published' : 'Draft'}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {formatDate(blog.createdAt)}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditBlog(blog)}
                          disabled={isProcessing || loading}
                          className="flex items-center gap-1"
                        >
                          <Edit className="h-4 w-4" />
                          Edit
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTogglePublish(blog)}
                          disabled={isProcessing || loading}
                          className={`flex items-center gap-1 ${
                            blog.published
                              ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50'
                              : 'text-green-600 hover:text-green-800 hover:bg-green-50'
                          }`}
                        >
                          {isProcessing ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                          {blog.published ? 'Unpublish' : 'Publish'}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openDeleteDialog(blog)}
                          disabled={isProcessing || loading}
                          className="flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-burgundy-100 mb-4">
            <FileText className="h-8 w-8 text-burgundy-600" />
          </div>
          <h2 className="text-xl font-medium text-gray-700 mb-2">No blog posts found</h2>
          <p className="text-gray-500 mb-4">Get started by creating your first blog post.</p>
          <Button
            onClick={handleCreateBlog}
            className="bg-burgundy-600 hover:bg-burgundy-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Your First Post
          </Button>
        </div>
      )}

      {/* Blog Editor Modal */}
      <BlogEditor
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSaveBlog}
        blog={editingBlog}
        isLoading={isProcessing}
        mode={editorMode}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Delete Blog Post
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the blog post <strong>"{blogToDelete?.title}"</strong>?
              <br /><br />
              {blogToDelete?.published ? (
                <div className="bg-orange-50 border border-orange-200 rounded-md p-3 mb-3">
                  <p className="text-orange-800 text-sm">
                    <strong>Warning:</strong> This blog post is currently published and visible on the public blog page.
                    Deleting it will remove it from both the admin dashboard and the public blog.
                  </p>
                </div>
              ) : (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
                  <p className="text-blue-800 text-sm">
                    This is a draft post that is only visible in the admin dashboard.
                  </p>
                </div>
              )}
              This action is permanent and cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteBlog}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isDeleting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Deleting...
                </>
              ) : (
                'Delete Post'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
};

export default AdminBlogManager;

